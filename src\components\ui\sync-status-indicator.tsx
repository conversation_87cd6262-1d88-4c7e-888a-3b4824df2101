'use client';

import React, { useState, useEffect } from 'react';
import { Cloud, CloudOff, RefreshCw, CheckCircle, AlertCircle, Wifi, WifiOff } from 'lucide-react';
import { cn } from '@/lib/utils';
import { showToast } from '@/components/ui/toast';
import LocalStorageManager from '@/lib/local-storage-manager';
import BatchSyncService from '@/lib/batch-sync-service';

interface SyncStatusIndicatorProps {
  className?: string;
  variant?: 'floating' | 'inline' | 'compact';
  showDetails?: boolean;
}

interface SyncStatus {
  pendingCount: number;
  lastSyncTime: number;
  hasErrors: boolean;
  errors: string[];
  isSyncing: boolean;
  isOnline: boolean;
}

/**
 * 同步状态指示器组件
 * 显示本地数据与服务器的同步状态，提供手动同步功能
 */
export function SyncStatusIndicator({
  className,
  variant = 'floating',
  showDetails = false
}: SyncStatusIndicatorProps) {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    pendingCount: 0,
    lastSyncTime: 0,
    hasErrors: false,
    errors: [],
    isSyncing: false,
    isOnline: navigator.onLine,
  });

  const [localStorageManager] = useState(() => LocalStorageManager.getInstance());
  const [batchSyncService] = useState(() => BatchSyncService.getInstance());
  const [isExpanded, setIsExpanded] = useState(false);

  // 更新同步状态
  const updateSyncStatus = () => {
    const localStatus = localStorageManager.getSyncStatus();
    const serviceStatus = batchSyncService.getSyncStatus();
    
    setSyncStatus(prev => ({
      ...prev,
      pendingCount: localStatus.pendingCount,
      lastSyncTime: localStatus.lastSyncTime,
      hasErrors: localStatus.hasErrors,
      errors: localStatus.errors,
      isSyncing: serviceStatus.isSyncing,
    }));
  };

  // 监听网络状态变化
  useEffect(() => {
    const handleOnline = () => setSyncStatus(prev => ({ ...prev, isOnline: true }));
    const handleOffline = () => setSyncStatus(prev => ({ ...prev, isOnline: false }));

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // 定期更新同步状态
  useEffect(() => {
    updateSyncStatus();
    const interval = setInterval(updateSyncStatus, 5000); // 每5秒更新一次

    return () => clearInterval(interval);
  }, [localStorageManager, batchSyncService]);

  // 手动同步
  const handleManualSync = async () => {
    if (!syncStatus.isOnline) {
      showToast({
        message: '网络连接不可用，无法同步',
        type: 'error',
        duration: 3000,
        position: 'top-center'
      });
      return;
    }

    if (syncStatus.isSyncing) {
      showToast({
        message: '正在同步中，请稍候',
        type: 'info',
        duration: 2000,
        position: 'top-center'
      });
      return;
    }

    try {
      const result = await batchSyncService.manualSync();
      
      if (result.success) {
        showToast({
          message: `同步完成！处理了 ${result.syncedCount} 个操作`,
          type: 'success',
          duration: 3000,
          position: 'top-center'
        });
      } else {
        showToast({
          message: `同步失败：${result.errors.join(', ')}`,
          type: 'error',
          duration: 5000,
          position: 'top-center'
        });
      }
    } catch (error) {
      showToast({
        message: '同步失败，请稍后重试',
        type: 'error',
        duration: 3000,
        position: 'top-center'
      });
    }

    updateSyncStatus();
  };

  // 获取状态图标和颜色
  const getStatusIcon = () => {
    if (!syncStatus.isOnline) {
      return { icon: WifiOff, color: 'text-gray-400', bgColor: 'bg-gray-100' };
    }
    
    if (syncStatus.isSyncing) {
      return { icon: RefreshCw, color: 'text-blue-500', bgColor: 'bg-blue-100', animate: 'animate-spin' };
    }
    
    if (syncStatus.hasErrors) {
      return { icon: AlertCircle, color: 'text-red-500', bgColor: 'bg-red-100' };
    }
    
    if (syncStatus.pendingCount > 0) {
      return { icon: Cloud, color: 'text-yellow-500', bgColor: 'bg-yellow-100' };
    }
    
    return { icon: CheckCircle, color: 'text-green-500', bgColor: 'bg-green-100' };
  };

  // 获取状态文本
  const getStatusText = () => {
    if (!syncStatus.isOnline) return '离线';
    if (syncStatus.isSyncing) return '同步中...';
    if (syncStatus.hasErrors) return '同步错误';
    if (syncStatus.pendingCount > 0) return `${syncStatus.pendingCount} 个待同步`;
    return '已同步';
  };

  // 格式化最后同步时间
  const formatLastSyncTime = () => {
    if (!syncStatus.lastSyncTime) return '从未同步';
    
    const now = Date.now();
    const diff = now - syncStatus.lastSyncTime;
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return '刚刚同步';
    if (minutes < 60) return `${minutes} 分钟前`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours} 小时前`;
    
    const days = Math.floor(hours / 24);
    return `${days} 天前`;
  };

  const { icon: StatusIcon, color, bgColor, animate } = getStatusIcon();

  if (variant === 'compact') {
    return (
      <button
        onClick={handleManualSync}
        className={cn(
          'flex items-center gap-2 px-3 py-1 rounded-full text-sm transition-colors',
          bgColor,
          color,
          'hover:opacity-80',
          className
        )}
        title={`${getStatusText()} - 点击手动同步`}
      >
        <StatusIcon className={cn('w-4 h-4', animate)} />
        {showDetails && <span>{getStatusText()}</span>}
      </button>
    );
  }

  if (variant === 'inline') {
    return (
      <div className={cn('flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg', className)}>
        <div className={cn('p-2 rounded-full', bgColor)}>
          <StatusIcon className={cn('w-5 h-5', color, animate)} />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {getStatusText()}
            </span>
            <button
              onClick={handleManualSync}
              disabled={syncStatus.isSyncing || !syncStatus.isOnline}
              className="text-xs text-blue-600 hover:text-blue-700 disabled:text-gray-400 disabled:cursor-not-allowed"
            >
              手动同步
            </button>
          </div>
          
          {showDetails && (
            <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              最后同步：{formatLastSyncTime()}
            </div>
          )}
        </div>
      </div>
    );
  }

  // 浮动模式
  return (
    <div className={cn('fixed bottom-4 right-4 z-50', className)}>
      <div
        className={cn(
          'bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700',
          'transition-all duration-300',
          isExpanded ? 'w-80' : 'w-auto'
        )}
      >
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center gap-3 p-3 w-full text-left hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
        >
          <div className={cn('p-2 rounded-full', bgColor)}>
            <StatusIcon className={cn('w-5 h-5', color, animate)} />
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {getStatusText()}
            </div>
            {syncStatus.pendingCount > 0 && (
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {syncStatus.pendingCount} 个操作待同步
              </div>
            )}
          </div>
        </button>

        {isExpanded && (
          <div className="px-3 pb-3 border-t border-gray-200 dark:border-gray-700">
            <div className="mt-3 space-y-2">
              <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400">
                <span>最后同步</span>
                <span>{formatLastSyncTime()}</span>
              </div>
              
              {syncStatus.hasErrors && (
                <div className="text-xs text-red-600 dark:text-red-400">
                  <div className="font-medium">同步错误：</div>
                  <div className="mt-1 max-h-20 overflow-y-auto">
                    {syncStatus.errors.slice(-3).map((error, index) => (
                      <div key={index} className="truncate">{error}</div>
                    ))}
                  </div>
                </div>
              )}
              
              <button
                onClick={handleManualSync}
                disabled={syncStatus.isSyncing || !syncStatus.isOnline}
                className={cn(
                  'w-full mt-3 px-3 py-2 text-sm rounded-md transition-colors',
                  'bg-blue-500 text-white hover:bg-blue-600',
                  'disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed'
                )}
              >
                {syncStatus.isSyncing ? '同步中...' : '立即同步'}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
