/**
 * 本地存储管理器 - 统一管理所有用户交互数据
 * 支持点赞、收藏、评论等功能的本地存储和批量同步
 */

export interface UserInteraction {
  id: string;
  type: 'like' | 'unlike' | 'bookmark' | 'unbookmark' | 'comment_like' | 'comment_unlike';
  targetId: string; // postId 或 commentId
  targetType: 'post' | 'comment';
  userId?: string;
  ipAddress?: string;
  timestamp: number;
  synced: boolean; // 是否已同步到服务器
  retryCount: number; // 重试次数
}

export interface LocalStorageData {
  interactions: UserInteraction[];
  lastSyncTime: number;
  pendingSync: boolean;
  syncErrors: string[];
}

export interface PostStats {
  likes: number;
  bookmarks: number;
  comments: number;
  shares: number;
}

export interface CommentStats {
  [commentId: string]: {
    likes: number;
    isLiked: boolean;
  };
}

export interface UserStates {
  posts: {
    [postId: string]: {
      isLiked: boolean;
      isBookmarked: boolean;
    };
  };
  comments: CommentStats;
}

class LocalStorageManager {
  private static instance: LocalStorageManager;
  private readonly STORAGE_KEY = 'mystical_interactions';
  private readonly MAX_RETRY_COUNT = 3;
  private readonly SYNC_INTERVAL = 30000; // 30秒
  private syncTimer: NodeJS.Timeout | null = null;

  private constructor() {
    this.initializeStorage();
    this.startAutoSync();
    this.setupBeforeUnloadSync();
  }

  public static getInstance(): LocalStorageManager {
    if (!LocalStorageManager.instance) {
      LocalStorageManager.instance = new LocalStorageManager();
    }
    return LocalStorageManager.instance;
  }

  /**
   * 初始化本地存储
   */
  private initializeStorage(): void {
    const data = this.getStorageData();
    if (!data) {
      this.setStorageData({
        interactions: [],
        lastSyncTime: 0,
        pendingSync: false,
        syncErrors: [],
      });
    }
  }

  /**
   * 获取本地存储数据
   */
  private getStorageData(): LocalStorageData | null {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Failed to parse localStorage data:', error);
      return null;
    }
  }

  /**
   * 设置本地存储数据
   */
  private setStorageData(data: LocalStorageData): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
    }
  }

  /**
   * 添加用户交互记录
   */
  public addInteraction(interaction: Omit<UserInteraction, 'id' | 'timestamp' | 'synced' | 'retryCount'>): string {
    const data = this.getStorageData();
    if (!data) return '';

    const newInteraction: UserInteraction = {
      ...interaction,
      id: this.generateId(),
      timestamp: Date.now(),
      synced: false,
      retryCount: 0,
    };

    // 检查是否有相反的操作，如果有则移除
    const oppositeType = this.getOppositeType(interaction.type);
    if (oppositeType) {
      data.interactions = data.interactions.filter(
        item => !(item.targetId === interaction.targetId && 
                 item.targetType === interaction.targetType && 
                 item.type === oppositeType)
      );
    }

    data.interactions.push(newInteraction);
    data.pendingSync = true;
    this.setStorageData(data);

    return newInteraction.id;
  }

  /**
   * 获取相反的操作类型
   */
  private getOppositeType(type: UserInteraction['type']): UserInteraction['type'] | null {
    const opposites: Record<string, UserInteraction['type']> = {
      'like': 'unlike',
      'unlike': 'like',
      'bookmark': 'unbookmark',
      'unbookmark': 'bookmark',
      'comment_like': 'comment_unlike',
      'comment_unlike': 'comment_like',
    };
    return opposites[type] || null;
  }

  /**
   * 获取用户当前状态
   */
  public getUserStates(): UserStates {
    const data = this.getStorageData();
    if (!data) return { posts: {}, comments: {} };

    const states: UserStates = { posts: {}, comments: {} };

    data.interactions.forEach(interaction => {
      if (interaction.targetType === 'post') {
        if (!states.posts[interaction.targetId]) {
          states.posts[interaction.targetId] = { isLiked: false, isBookmarked: false };
        }

        if (interaction.type === 'like') {
          states.posts[interaction.targetId].isLiked = true;
        } else if (interaction.type === 'unlike') {
          states.posts[interaction.targetId].isLiked = false;
        } else if (interaction.type === 'bookmark') {
          states.posts[interaction.targetId].isBookmarked = true;
        } else if (interaction.type === 'unbookmark') {
          states.posts[interaction.targetId].isBookmarked = false;
        }
      } else if (interaction.targetType === 'comment') {
        if (!states.comments[interaction.targetId]) {
          states.comments[interaction.targetId] = { likes: 0, isLiked: false };
        }

        if (interaction.type === 'comment_like') {
          states.comments[interaction.targetId].isLiked = true;
        } else if (interaction.type === 'comment_unlike') {
          states.comments[interaction.targetId].isLiked = false;
        }
      }
    });

    return states;
  }

  /**
   * 获取待同步的交互记录
   */
  public getPendingInteractions(): UserInteraction[] {
    const data = this.getStorageData();
    if (!data) return [];

    return data.interactions.filter(interaction => 
      !interaction.synced && interaction.retryCount < this.MAX_RETRY_COUNT
    );
  }

  /**
   * 标记交互为已同步
   */
  public markAsSynced(interactionIds: string[]): void {
    const data = this.getStorageData();
    if (!data) return;

    data.interactions.forEach(interaction => {
      if (interactionIds.includes(interaction.id)) {
        interaction.synced = true;
      }
    });

    // 检查是否还有待同步的数据
    const hasPending = data.interactions.some(interaction => !interaction.synced);
    data.pendingSync = hasPending;
    data.lastSyncTime = Date.now();

    this.setStorageData(data);
  }

  /**
   * 标记同步失败，增加重试次数
   */
  public markSyncFailed(interactionIds: string[], error: string): void {
    const data = this.getStorageData();
    if (!data) return;

    data.interactions.forEach(interaction => {
      if (interactionIds.includes(interaction.id)) {
        interaction.retryCount++;
      }
    });

    data.syncErrors.push(`${new Date().toISOString()}: ${error}`);
    // 只保留最近10个错误
    if (data.syncErrors.length > 10) {
      data.syncErrors = data.syncErrors.slice(-10);
    }

    this.setStorageData(data);
  }

  /**
   * 清理已同步的旧数据
   */
  public cleanupSyncedData(): void {
    const data = this.getStorageData();
    if (!data) return;

    const oneWeekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
    data.interactions = data.interactions.filter(interaction => 
      !interaction.synced || interaction.timestamp > oneWeekAgo
    );

    this.setStorageData(data);
  }

  /**
   * 获取同步状态
   */
  public getSyncStatus(): {
    pendingCount: number;
    lastSyncTime: number;
    hasErrors: boolean;
    errors: string[];
  } {
    const data = this.getStorageData();
    if (!data) return { pendingCount: 0, lastSyncTime: 0, hasErrors: false, errors: [] };

    const pendingCount = data.interactions.filter(i => !i.synced).length;
    return {
      pendingCount,
      lastSyncTime: data.lastSyncTime,
      hasErrors: data.syncErrors.length > 0,
      errors: data.syncErrors,
    };
  }

  /**
   * 启动自动同步
   */
  private startAutoSync(): void {
    if (typeof window === 'undefined') return;

    this.syncTimer = setInterval(() => {
      this.triggerSync();
    }, this.SYNC_INTERVAL);
  }

  /**
   * 设置页面卸载时同步
   */
  private setupBeforeUnloadSync(): void {
    if (typeof window === 'undefined') return;

    window.addEventListener('beforeunload', () => {
      this.triggerSync(true); // 同步模式
    });

    // 页面可见性变化时也尝试同步
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        this.triggerSync();
      }
    });
  }

  /**
   * 触发同步
   */
  private triggerSync(sync = false): void {
    const data = this.getStorageData();
    if (!data || !data.pendingSync) return;

    // 触发自定义事件，让同步服务处理
    window.dispatchEvent(new CustomEvent('localStorageSync', { 
      detail: { sync, interactions: this.getPendingInteractions() }
    }));
  }

  /**
   * 手动触发同步
   */
  public manualSync(): void {
    this.triggerSync();
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 销毁实例
   */
  public destroy(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
  }
}

export default LocalStorageManager;
