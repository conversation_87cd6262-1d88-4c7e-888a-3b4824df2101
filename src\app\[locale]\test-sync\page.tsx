'use client';

import React, { useState, useEffect } from 'react';
import { Heart, Bookmark, ThumbsUp } from 'lucide-react';
import LocalStorageManager from '@/lib/local-storage-manager';
import BatchSyncService from '@/lib/batch-sync-service';
import { SyncStatusIndicator } from '@/components/ui/sync-status-indicator';
import { showToast } from '@/components/ui/toast';

/**
 * 批量同步功能测试页面
 */
export default function TestSyncPage() {
  const [localStorageManager] = useState(() => LocalStorageManager.getInstance());
  const [batchSyncService] = useState(() => BatchSyncService.getInstance());
  const [userStates, setUserStates] = useState<any>({});
  const [syncStatus, setSyncStatus] = useState<any>({});

  // 更新状态
  const updateStates = () => {
    setUserStates(localStorageManager.getUserStates());
    setSyncStatus(localStorageManager.getSyncStatus());
  };

  useEffect(() => {
    updateStates();
    const interval = setInterval(updateStates, 1000);
    return () => clearInterval(interval);
  }, [localStorageManager]);

  // 模拟文章点赞
  const handleLikePost = (postId: string) => {
    const currentState = userStates.posts?.[postId]?.isLiked || false;
    const newIsLiked = !currentState;
    
    localStorageManager.addInteraction({
      type: newIsLiked ? 'like' : 'unlike',
      targetId: postId,
      targetType: 'post',
      userId: 'test-user-123',
    });

    showToast({
      message: newIsLiked ? '❤️ 已点赞' : '已取消点赞',
      type: 'success',
      duration: 1500,
      position: 'bottom-center'
    });

    updateStates();
  };

  // 模拟文章收藏
  const handleBookmarkPost = (postId: string) => {
    const currentState = userStates.posts?.[postId]?.isBookmarked || false;
    const newIsBookmarked = !currentState;
    
    localStorageManager.addInteraction({
      type: newIsBookmarked ? 'bookmark' : 'unbookmark',
      targetId: postId,
      targetType: 'post',
      userId: 'test-user-123',
    });

    showToast({
      message: newIsBookmarked ? '🔖 已收藏' : '已取消收藏',
      type: 'success',
      duration: 1500,
      position: 'bottom-center'
    });

    updateStates();
  };

  // 模拟评论点赞
  const handleLikeComment = (commentId: string) => {
    const currentState = userStates.comments?.[commentId]?.isLiked || false;
    const newIsLiked = !currentState;
    
    localStorageManager.addInteraction({
      type: newIsLiked ? 'comment_like' : 'comment_unlike',
      targetId: commentId,
      targetType: 'comment',
      userId: 'test-user-123',
    });

    showToast({
      message: newIsLiked ? '👍 已点赞评论' : '已取消点赞评论',
      type: 'success',
      duration: 1500,
      position: 'bottom-center'
    });

    updateStates();
  };

  // 手动同步
  const handleManualSync = async () => {
    try {
      const result = await batchSyncService.manualSync();
      console.log('Sync result:', result);
      updateStates();
    } catch (error) {
      console.error('Sync failed:', error);
    }
  };

  // 清理数据
  const handleClearData = () => {
    localStorage.removeItem('mystical_interactions');
    updateStates();
    showToast({
      message: '本地数据已清理',
      type: 'info',
      duration: 2000,
      position: 'top-center'
    });
  };

  const testPosts = [
    { id: 'post-1', title: '塔罗牌入门指南' },
    { id: 'post-2', title: '星座运势解析' },
    { id: 'post-3', title: '数字命理学基础' },
  ];

  const testComments = [
    { id: 'comment-1', content: '这篇文章很有用！' },
    { id: 'comment-2', content: '感谢分享，学到了很多' },
    { id: 'comment-3', content: '期待更多相关内容' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            批量同步功能测试
          </h1>

          {/* 同步状态概览 */}
          <div className="mb-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h2 className="text-lg font-semibold mb-4">同步状态</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-600 dark:text-gray-400">待同步：</span>
                <span className="font-medium">{syncStatus.pendingCount || 0}</span>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">错误：</span>
                <span className="font-medium text-red-500">{syncStatus.errors?.length || 0}</span>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">最后同步：</span>
                <span className="font-medium">
                  {syncStatus.lastSyncTime ? new Date(syncStatus.lastSyncTime).toLocaleTimeString() : '从未'}
                </span>
              </div>
              <div>
                <button
                  onClick={handleManualSync}
                  className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                >
                  手动同步
                </button>
              </div>
            </div>
          </div>

          {/* 文章测试区域 */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold mb-4">文章交互测试</h2>
            <div className="space-y-4">
              {testPosts.map(post => {
                const postState = userStates.posts?.[post.id];
                return (
                  <div key={post.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                    <span className="font-medium">{post.title}</span>
                    <div className="flex items-center gap-4">
                      <button
                        onClick={() => handleLikePost(post.id)}
                        className={`flex items-center gap-2 px-3 py-1 rounded transition-colors ${
                          postState?.isLiked 
                            ? 'bg-red-100 text-red-600' 
                            : 'bg-gray-100 text-gray-600 hover:bg-red-50'
                        }`}
                      >
                        <Heart className={`w-4 h-4 ${postState?.isLiked ? 'fill-current' : ''}`} />
                        点赞
                      </button>
                      <button
                        onClick={() => handleBookmarkPost(post.id)}
                        className={`flex items-center gap-2 px-3 py-1 rounded transition-colors ${
                          postState?.isBookmarked 
                            ? 'bg-yellow-100 text-yellow-600' 
                            : 'bg-gray-100 text-gray-600 hover:bg-yellow-50'
                        }`}
                      >
                        <Bookmark className={`w-4 h-4 ${postState?.isBookmarked ? 'fill-current' : ''}`} />
                        收藏
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* 评论测试区域 */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold mb-4">评论交互测试</h2>
            <div className="space-y-4">
              {testComments.map(comment => {
                const commentState = userStates.comments?.[comment.id];
                return (
                  <div key={comment.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                    <span>{comment.content}</span>
                    <button
                      onClick={() => handleLikeComment(comment.id)}
                      className={`flex items-center gap-2 px-3 py-1 rounded transition-colors ${
                        commentState?.isLiked 
                          ? 'bg-blue-100 text-blue-600' 
                          : 'bg-gray-100 text-gray-600 hover:bg-blue-50'
                      }`}
                    >
                      <ThumbsUp className={`w-4 h-4 ${commentState?.isLiked ? 'fill-current' : ''}`} />
                      点赞
                    </button>
                  </div>
                );
              })}
            </div>
          </div>

          {/* 控制按钮 */}
          <div className="flex gap-4">
            <button
              onClick={handleClearData}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
            >
              清理本地数据
            </button>
          </div>

          {/* 内联同步状态指示器 */}
          <div className="mt-8">
            <SyncStatusIndicator variant="inline" showDetails={true} />
          </div>
        </div>
      </div>

      {/* 浮动同步状态指示器 */}
      <SyncStatusIndicator variant="floating" />
    </div>
  );
}
