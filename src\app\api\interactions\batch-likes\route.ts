import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import DatabaseService from '@/lib/database-service';

// 批量点赞请求验证
const batchLikesSchema = z.object({
  interactions: z.array(z.object({
    id: z.string(),
    type: z.enum(['like', 'unlike']),
    targetId: z.string(),
    targetType: z.literal('post'),
    userId: z.string().optional(),
    ipAddress: z.string().optional(),
    timestamp: z.number(),
  }))
});

/**
 * 批量处理文章点赞/取消点赞
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证请求数据
    const validation = batchLikesSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { interactions } = validation.data;
    
    if (interactions.length === 0) {
      return NextResponse.json({ success: true, processed: 0 });
    }

    // 获取客户端IP地址
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    '127.0.0.1';

    const results = {
      processed: 0,
      errors: [] as string[],
    };

    // 按文章ID分组处理
    const groupedByPost = interactions.reduce((groups, interaction) => {
      if (!groups[interaction.targetId]) {
        groups[interaction.targetId] = [];
      }
      groups[interaction.targetId].push(interaction);
      return groups;
    }, {} as Record<string, typeof interactions>);

    // 处理每个文章的点赞操作
    for (const [postId, postInteractions] of Object.entries(groupedByPost)) {
      try {
        // 验证文章是否存在
        const post = await DatabaseService.getPostById(postId);
        if (!post) {
          results.errors.push(`Post not found: ${postId}`);
          continue;
        }

        // 按时间排序，确保操作顺序正确
        const sortedInteractions = postInteractions.sort((a, b) => a.timestamp - b.timestamp);
        
        // 处理每个交互
        for (const interaction of sortedInteractions) {
          try {
            const userId = interaction.userId;
            const ipAddress = interaction.ipAddress || clientIP;

            if (interaction.type === 'like') {
              await DatabaseService.likePost(postId, userId, ipAddress);
            } else if (interaction.type === 'unlike') {
              await DatabaseService.unlikePost(postId, userId, ipAddress);
            }

            results.processed++;
          } catch (error) {
            console.error(`Error processing interaction ${interaction.id}:`, error);
            results.errors.push(`Failed to process interaction ${interaction.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        }
      } catch (error) {
        console.error(`Error processing post ${postId}:`, error);
        results.errors.push(`Failed to process post ${postId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return NextResponse.json({
      success: results.errors.length === 0,
      processed: results.processed,
      errors: results.errors,
      total: interactions.length,
    });

  } catch (error) {
    console.error('Error in batch likes processing:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
