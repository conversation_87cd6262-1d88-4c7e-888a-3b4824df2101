import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import DatabaseService from '@/lib/database-service';

// 批量同步请求验证
const batchSyncSchema = z.object({
  interactions: z.array(z.object({
    id: z.string(),
    type: z.enum(['like', 'unlike', 'bookmark', 'unbookmark', 'comment_like', 'comment_unlike']),
    targetId: z.string(),
    targetType: z.enum(['post', 'comment']),
    userId: z.string().optional(),
    ipAddress: z.string().optional(),
    timestamp: z.number(),
  }))
});

/**
 * 通用批量同步端点
 * 处理所有类型的用户交互同步
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证请求数据
    const validation = batchSyncSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { interactions } = validation.data;
    
    if (interactions.length === 0) {
      return NextResponse.json({ success: true, processed: 0 });
    }

    // 获取客户端IP地址
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    '127.0.0.1';

    const results = {
      processed: 0,
      errors: [] as string[],
    };

    // 按类型分组处理
    const postInteractions = interactions.filter(i => i.targetType === 'post');
    const commentInteractions = interactions.filter(i => i.targetType === 'comment');

    // 处理文章交互
    if (postInteractions.length > 0) {
      try {
        const postResult = await processPostInteractions(postInteractions, clientIP);
        results.processed += postResult.processed;
        results.errors.push(...postResult.errors);
      } catch (error) {
        console.error('Error processing post interactions:', error);
        results.errors.push(`Post interactions failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // 处理评论交互
    if (commentInteractions.length > 0) {
      try {
        const commentResult = await processCommentInteractions(commentInteractions, clientIP);
        results.processed += commentResult.processed;
        results.errors.push(...commentResult.errors);
      } catch (error) {
        console.error('Error processing comment interactions:', error);
        results.errors.push(`Comment interactions failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return NextResponse.json({
      success: results.errors.length === 0,
      processed: results.processed,
      errors: results.errors,
      total: interactions.length,
    });

  } catch (error) {
    console.error('Error in batch sync:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * 处理文章相关交互
 */
async function processPostInteractions(interactions: any[], clientIP: string) {
  const results = { processed: 0, errors: [] as string[] };

  // 按文章ID分组
  const groupedByPost = interactions.reduce((groups, interaction) => {
    if (!groups[interaction.targetId]) {
      groups[interaction.targetId] = [];
    }
    groups[interaction.targetId].push(interaction);
    return groups;
  }, {} as Record<string, any[]>);

  for (const [postId, postInteractions] of Object.entries(groupedByPost)) {
    try {
      // 验证文章是否存在
      const post = await DatabaseService.getBlogPostById(postId);
      if (!post) {
        results.errors.push(`Post not found: ${postId}`);
        continue;
      }

      // 按时间排序
      const sortedInteractions = postInteractions.sort((a, b) => a.timestamp - b.timestamp);
      
      for (const interaction of sortedInteractions) {
        try {
          const userId = interaction.userId;
          const ipAddress = interaction.ipAddress || clientIP;

          switch (interaction.type) {
            case 'like':
              await DatabaseService.likePost(postId, userId, ipAddress);
              break;
            case 'unlike':
              await DatabaseService.unlikePost(postId, userId, ipAddress);
              break;
            case 'bookmark':
              if (userId) {
                await DatabaseService.bookmarkPost(postId, userId);
              } else {
                results.errors.push(`Bookmark requires user login: ${interaction.id}`);
                continue;
              }
              break;
            case 'unbookmark':
              if (userId) {
                await DatabaseService.unbookmarkPost(postId, userId);
              } else {
                results.errors.push(`Unbookmark requires user login: ${interaction.id}`);
                continue;
              }
              break;
          }

          results.processed++;
        } catch (error) {
          console.error(`Error processing interaction ${interaction.id}:`, error);
          results.errors.push(`Failed to process interaction ${interaction.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    } catch (error) {
      console.error(`Error processing post ${postId}:`, error);
      results.errors.push(`Failed to process post ${postId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return results;
}

/**
 * 处理评论相关交互
 */
async function processCommentInteractions(interactions: any[], clientIP: string) {
  const results = { processed: 0, errors: [] as string[] };

  // 按评论ID分组
  const groupedByComment = interactions.reduce((groups, interaction) => {
    if (!groups[interaction.targetId]) {
      groups[interaction.targetId] = [];
    }
    groups[interaction.targetId].push(interaction);
    return groups;
  }, {} as Record<string, any[]>);

  for (const [commentId, commentInteractions] of Object.entries(groupedByComment)) {
    try {
      // 验证评论是否存在
      const comment = await DatabaseService.getCommentById(commentId);
      if (!comment) {
        results.errors.push(`Comment not found: ${commentId}`);
        continue;
      }

      // 按时间排序
      const sortedInteractions = commentInteractions.sort((a, b) => a.timestamp - b.timestamp);
      
      for (const interaction of sortedInteractions) {
        try {
          const userId = interaction.userId;
          const ipAddress = interaction.ipAddress || clientIP;

          // 检查当前点赞状态
          const existingLike = await DatabaseService.getCommentLike(commentId, userId, ipAddress);

          if (interaction.type === 'comment_like') {
            if (!existingLike) {
              await DatabaseService.addCommentLike(commentId, userId, ipAddress);
            }
          } else if (interaction.type === 'comment_unlike') {
            if (existingLike) {
              await DatabaseService.removeCommentLike(commentId, userId, ipAddress);
            }
          }

          results.processed++;
        } catch (error) {
          console.error(`Error processing interaction ${interaction.id}:`, error);
          results.errors.push(`Failed to process interaction ${interaction.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    } catch (error) {
      console.error(`Error processing comment ${commentId}:`, error);
      results.errors.push(`Failed to process comment ${commentId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return results;
}
