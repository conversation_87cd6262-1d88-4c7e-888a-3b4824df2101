'use client';

import React, { useState, useEffect } from 'react';
import { ThumbsUp, Reply, Flag } from 'lucide-react';
import { createRoot } from 'react-dom/client';
import { cn } from '@/lib/utils';
import { showToast } from '@/components/ui/toast';
import { ReplyForm } from './ReplyForm';
import LocalStorageManager from '@/lib/local-storage-manager';
import BatchSyncService from '@/lib/batch-sync-service';

interface CommentInteractionsProps {
  postId: string;
  userId?: string;
  className?: string;
}

/**
 * 评论交互增强组件 - 本地存储优先
 * 为SSR渲染的评论内容添加交互功能
 * 所有交互立即存储在本地，批量同步到服务器
 */
export function CommentInteractions({ postId, userId, className }: CommentInteractionsProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [localStorageManager] = useState(() => LocalStorageManager.getInstance());
  const [batchSyncService] = useState(() => BatchSyncService.getInstance());

  // 初始化组件状态
  useEffect(() => {
    // 从本地存储管理器获取用户状态并同步到UI
    const userStates = localStorageManager.getUserStates();

    // 同步评论点赞状态到UI
    Object.entries(userStates.comments).forEach(([commentId, state]) => {
      updateCommentLikeUI(commentId, state.isLiked);
    });

    setIsInitialized(true);
  }, [localStorageManager]);

  // 处理点赞操作 - 本地存储优先
  const handleLikeComment = (commentId: string) => {
    // 获取当前状态
    const userStates = localStorageManager.getUserStates();
    const currentState = userStates.comments[commentId];
    const newIsLiked = !currentState?.isLiked;

    // 获取当前点赞数量进行乐观更新
    const countElement = document.querySelector(`[data-comment-id="${commentId}"] .like-count`);
    const currentCount = parseInt(countElement?.textContent || '0');
    const newCount = newIsLiked ? currentCount + 1 : Math.max(currentCount - 1, 0);

    // 立即更新UI显示（乐观更新）
    updateCommentLikeUI(commentId, newIsLiked);
    updateCommentLikeCount(commentId, newCount);

    // 添加交互记录到本地存储
    const interactionType = newIsLiked ? 'comment_like' : 'comment_unlike';
    localStorageManager.addInteraction({
      type: interactionType,
      targetId: commentId,
      targetType: 'comment',
      userId,
      ipAddress: undefined, // 将由批量同步服务获取
    });

    // 显示即时反馈
    showToast({
      message: newIsLiked ? '👍 已点赞' : '已取消点赞',
      type: 'success',
      duration: 1500,
      position: 'bottom-center'
    });
  };

  // 更新评论点赞UI状态
  const updateCommentLikeUI = (commentId: string, isLiked: boolean) => {
    const button = document.querySelector(`[data-comment-id="${commentId}"] .comment-like-button`);
    if (button) {
      const icon = button.querySelector('.like-icon');
      const text = button.querySelector('.like-text');
      
      if (icon) {
        if (isLiked) {
          icon.classList.add('text-red-500', 'fill-current');
          icon.classList.remove('text-gray-500', 'dark:text-gray-400');
        } else {
          icon.classList.remove('text-red-500', 'fill-current');
          icon.classList.add('text-gray-500', 'dark:text-gray-400');
        }
      }
      
      button.classList.toggle('text-red-500', isLiked);
      button.classList.toggle('text-gray-500', !isLiked);
      button.classList.toggle('dark:text-gray-400', !isLiked);
    }
  };

  // 更新评论点赞数量
  const updateCommentLikeCount = (commentId: string, count: number) => {
    const countElement = document.querySelector(`[data-comment-id="${commentId}"] .like-count`);
    if (countElement) {
      countElement.textContent = count.toString();
    }
  };

  // 处理回复操作
  const handleReplyComment = (commentId: string) => {
    const replyFormContainer = document.querySelector(`[data-reply-form="${commentId}"]`);
    if (!replyFormContainer) return;

    const isHidden = replyFormContainer.classList.contains('hidden');
    
    if (isHidden) {
      // 显示回复表单
      replyFormContainer.classList.remove('hidden');
      
      // 动态渲染回复表单
      const commentElement = document.querySelector(`[data-comment-id="${commentId}"]`);
      const userNameElement = commentElement?.querySelector('[itemProp="name"]');
      const userName = userNameElement?.textContent || '匿名用户';
      
      // 清空容器并渲染新的回复表单
      replyFormContainer.innerHTML = '';
      const root = createRoot(replyFormContainer);
      root.render(
        <ReplyForm
          postId={postId}
          parentCommentId={commentId}
          parentUserName={userName}
          userId={userId}
          allowGuests={true}
          onCancel={() => {
            replyFormContainer.classList.add('hidden');
            root.unmount();
          }}
          onReplyAdded={() => {
            // 回复成功后的处理将在ReplyForm组件内完成
          }}
        />
      );
    } else {
      // 隐藏回复表单
      replyFormContainer.classList.add('hidden');
      replyFormContainer.innerHTML = '';
    }
  };

  // 处理举报操作
  const handleReportComment = (commentId: string) => {
    showToast({
      message: '举报功能暂未开放',
      type: 'info',
      duration: 2000,
      position: 'top-center'
    });
  };

  // 为所有评论按钮添加事件监听器
  useEffect(() => {
    if (!isInitialized) return;

    const handleClick = (event: Event) => {
      const target = event.target as HTMLElement;
      const button = target.closest('button');
      if (!button) return;

      const commentElement = button.closest('[data-comment-id]');
      if (!commentElement) return;

      const commentId = commentElement.getAttribute('data-comment-id');
      if (!commentId) return;

      if (button.classList.contains('comment-like-button')) {
        event.preventDefault();
        handleLikeComment(commentId);
      } else if (button.classList.contains('comment-reply-button')) {
        event.preventDefault();
        handleReplyComment(commentId);
      } else if (button.classList.contains('comment-report-button')) {
        event.preventDefault();
        handleReportComment(commentId);
      }
    };

    document.addEventListener('click', handleClick);

    return () => {
      document.removeEventListener('click', handleClick);
    };
  }, [isInitialized, postId, userId, localStorageManager]);

  // 这个组件不渲染任何内容，只是添加交互功能
  return null;
}

/**
 * 评论交互按钮组件 - 用于SSR渲染时的占位按钮
 */
export function CommentActionButtons({ 
  commentId, 
  likeCount = 0,
  className 
}: { 
  commentId: string; 
  likeCount?: number;
  className?: string;
}) {
  return (
    <div className={cn('flex items-center gap-4 text-sm', className)}>
      <button
        className="comment-like-button flex items-center gap-1 transition-colors text-gray-500 dark:text-gray-400 hover:text-red-500"
        data-comment-id={commentId}
        data-initial-count={likeCount}
      >
        <ThumbsUp className="like-icon w-4 h-4" />
        <span className="like-count">{likeCount}</span>
      </button>

      <button
        className="comment-reply-button text-gray-500 dark:text-gray-400 hover:text-blue-500 transition-colors"
        data-comment-id={commentId}
      >
        <Reply className="w-4 h-4 inline mr-1" />
        回复
      </button>

      <button
        className="comment-report-button text-gray-500 dark:text-gray-400 hover:text-orange-500 transition-colors"
        data-comment-id={commentId}
      >
        <Flag className="w-4 h-4 inline mr-1" />
        举报
      </button>
    </div>
  );
}