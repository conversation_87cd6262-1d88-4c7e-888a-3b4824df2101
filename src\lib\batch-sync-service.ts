/**
 * 批量同步服务 - 处理本地存储数据与服务器的同步
 * 支持多种触发方式：定时同步、页面卸载同步、手动同步
 */

import LocalStorageManager, { UserInteraction } from './local-storage-manager';
import { showToast } from '@/components/ui/toast';

export interface SyncResult {
  success: boolean;
  syncedCount: number;
  failedCount: number;
  errors: string[];
}

export interface BatchSyncRequest {
  interactions: UserInteraction[];
  userId?: string;
  ipAddress?: string;
}

class BatchSyncService {
  private static instance: BatchSyncService;
  private localStorageManager: LocalStorageManager;
  private isSyncing = false;
  private syncQueue: UserInteraction[] = [];

  private constructor() {
    this.localStorageManager = LocalStorageManager.getInstance();
    this.setupEventListeners();
  }

  public static getInstance(): BatchSyncService {
    if (!BatchSyncService.instance) {
      BatchSyncService.instance = new BatchSyncService();
    }
    return BatchSyncService.instance;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (typeof window === 'undefined') return;

    // 监听本地存储同步事件
    window.addEventListener('localStorageSync', this.handleSyncEvent.bind(this));

    // 监听网络状态变化
    window.addEventListener('online', () => {
      console.log('Network restored, triggering sync...');
      this.syncPendingInteractions();
    });

    window.addEventListener('offline', () => {
      console.log('Network lost, interactions will be queued locally');
    });
  }

  /**
   * 处理同步事件
   */
  private async handleSyncEvent(event: CustomEvent): Promise<void> {
    const { sync, interactions } = event.detail;
    
    if (sync) {
      // 同步模式 - 使用 sendBeacon 或同步请求
      await this.syncWithBeacon(interactions);
    } else {
      // 异步模式 - 正常的异步同步
      await this.syncPendingInteractions();
    }
  }

  /**
   * 使用 sendBeacon 进行同步（页面卸载时）
   */
  private syncWithBeacon(interactions: UserInteraction[]): void {
    if (!interactions.length) return;

    const data = JSON.stringify({
      interactions: interactions.map(this.transformInteractionForAPI),
    });

    // 设置正确的Content-Type
    const blob = new Blob([data], { type: 'application/json' });
    const success = navigator.sendBeacon('/api/interactions/batch-sync', blob);

    if (success) {
      console.log('Beacon sync initiated for', interactions.length, 'interactions');
    } else {
      console.warn('Beacon sync failed, data will be retried on next visit');
    }
  }

  /**
   * 同步待处理的交互
   */
  public async syncPendingInteractions(showNotification = false): Promise<SyncResult> {
    if (this.isSyncing) {
      return { success: false, syncedCount: 0, failedCount: 0, errors: ['Sync already in progress'] };
    }

    const pendingInteractions = this.localStorageManager.getPendingInteractions();
    if (!pendingInteractions.length) {
      return { success: true, syncedCount: 0, failedCount: 0, errors: [] };
    }

    this.isSyncing = true;

    try {
      const result = await this.batchSyncToServer(pendingInteractions);
      
      if (showNotification) {
        if (result.success) {
          showToast({
            message: `成功同步 ${result.syncedCount} 个操作`,
            type: 'success',
            duration: 3000,
            position: 'bottom-center'
          });
        } else {
          showToast({
            message: `同步失败：${result.errors.join(', ')}`,
            type: 'error',
            duration: 5000,
            position: 'top-center'
          });
        }
      }

      return result;
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * 批量同步到服务器
   */
  private async batchSyncToServer(interactions: UserInteraction[]): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      syncedCount: 0,
      failedCount: 0,
      errors: []
    };

    try {
      // 使用统一的批量同步端点
      const response = await fetch('/api/interactions/batch-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          interactions: interactions.map(this.transformInteractionForAPI),
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();

      if (data.success) {
        result.syncedCount = data.processed;
        result.success = true;

        // 标记所有交互为已同步
        this.localStorageManager.markAsSynced(interactions.map(i => i.id));
      } else {
        result.failedCount = interactions.length;
        result.errors = data.errors || ['Unknown sync error'];
        result.success = false;

        // 标记同步失败
        this.localStorageManager.markSyncFailed(
          interactions.map(i => i.id),
          data.errors?.join(', ') || 'Unknown error'
        );
      }
    } catch (error) {
      result.failedCount = interactions.length;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      result.errors.push(errorMessage);
      result.success = false;

      this.localStorageManager.markSyncFailed(
        interactions.map(i => i.id),
        errorMessage
      );
    }

    return result;
  }



  /**
   * 转换交互数据为API格式
   */
  private transformInteractionForAPI(interaction: UserInteraction) {
    return {
      id: interaction.id,
      type: interaction.type,
      targetId: interaction.targetId,
      targetType: interaction.targetType,
      userId: interaction.userId,
      ipAddress: interaction.ipAddress,
      timestamp: interaction.timestamp,
    };
  }

  /**
   * 获取同步状态
   */
  public getSyncStatus() {
    return {
      isSyncing: this.isSyncing,
      queueLength: this.syncQueue.length,
      ...this.localStorageManager.getSyncStatus(),
    };
  }

  /**
   * 手动触发同步
   */
  public async manualSync(): Promise<SyncResult> {
    return this.syncPendingInteractions(true);
  }

  /**
   * 清理已同步的数据
   */
  public cleanupSyncedData(): void {
    this.localStorageManager.cleanupSyncedData();
  }

  /**
   * 重置同步状态（用于测试）
   */
  public resetSyncState(): void {
    this.isSyncing = false;
    this.syncQueue = [];
  }
}

export default BatchSyncService;
