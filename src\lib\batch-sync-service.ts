/**
 * 批量同步服务 - 处理本地存储数据与服务器的同步
 * 支持多种触发方式：定时同步、页面卸载同步、手动同步
 */

import LocalStorageManager, { UserInteraction } from './local-storage-manager';
import { showToast } from '@/components/ui/toast';

export interface SyncResult {
  success: boolean;
  syncedCount: number;
  failedCount: number;
  errors: string[];
}

export interface BatchSyncRequest {
  interactions: UserInteraction[];
  userId?: string;
  ipAddress?: string;
}

class BatchSyncService {
  private static instance: BatchSyncService;
  private localStorageManager: LocalStorageManager;
  private isSyncing = false;
  private syncQueue: UserInteraction[] = [];

  private constructor() {
    this.localStorageManager = LocalStorageManager.getInstance();
    this.setupEventListeners();
  }

  public static getInstance(): BatchSyncService {
    if (!BatchSyncService.instance) {
      BatchSyncService.instance = new BatchSyncService();
    }
    return BatchSyncService.instance;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (typeof window === 'undefined') return;

    // 监听本地存储同步事件
    window.addEventListener('localStorageSync', this.handleSyncEvent.bind(this));

    // 监听网络状态变化
    window.addEventListener('online', () => {
      console.log('Network restored, triggering sync...');
      this.syncPendingInteractions();
    });

    window.addEventListener('offline', () => {
      console.log('Network lost, interactions will be queued locally');
    });
  }

  /**
   * 处理同步事件
   */
  private async handleSyncEvent(event: CustomEvent): Promise<void> {
    const { sync, interactions } = event.detail;
    
    if (sync) {
      // 同步模式 - 使用 sendBeacon 或同步请求
      await this.syncWithBeacon(interactions);
    } else {
      // 异步模式 - 正常的异步同步
      await this.syncPendingInteractions();
    }
  }

  /**
   * 使用 sendBeacon 进行同步（页面卸载时）
   */
  private syncWithBeacon(interactions: UserInteraction[]): void {
    if (!interactions.length) return;

    const data = JSON.stringify({
      interactions: interactions.map(this.transformInteractionForAPI),
    });

    const success = navigator.sendBeacon('/api/interactions/batch-sync', data);
    
    if (success) {
      console.log('Beacon sync initiated for', interactions.length, 'interactions');
    } else {
      console.warn('Beacon sync failed, data will be retried on next visit');
    }
  }

  /**
   * 同步待处理的交互
   */
  public async syncPendingInteractions(showNotification = false): Promise<SyncResult> {
    if (this.isSyncing) {
      return { success: false, syncedCount: 0, failedCount: 0, errors: ['Sync already in progress'] };
    }

    const pendingInteractions = this.localStorageManager.getPendingInteractions();
    if (!pendingInteractions.length) {
      return { success: true, syncedCount: 0, failedCount: 0, errors: [] };
    }

    this.isSyncing = true;

    try {
      const result = await this.batchSyncToServer(pendingInteractions);
      
      if (showNotification) {
        if (result.success) {
          showToast({
            message: `成功同步 ${result.syncedCount} 个操作`,
            type: 'success',
            duration: 3000,
            position: 'bottom-center'
          });
        } else {
          showToast({
            message: `同步失败：${result.errors.join(', ')}`,
            type: 'error',
            duration: 5000,
            position: 'top-center'
          });
        }
      }

      return result;
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * 批量同步到服务器
   */
  private async batchSyncToServer(interactions: UserInteraction[]): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      syncedCount: 0,
      failedCount: 0,
      errors: []
    };

    // 按类型分组处理
    const groupedInteractions = this.groupInteractionsByType(interactions);

    for (const [type, typeInteractions] of Object.entries(groupedInteractions)) {
      try {
        const syncResult = await this.syncInteractionGroup(type, typeInteractions);
        
        if (syncResult.success) {
          result.syncedCount += typeInteractions.length;
          this.localStorageManager.markAsSynced(typeInteractions.map(i => i.id));
        } else {
          result.failedCount += typeInteractions.length;
          result.errors.push(`${type}: ${syncResult.error}`);
          this.localStorageManager.markSyncFailed(
            typeInteractions.map(i => i.id), 
            syncResult.error || 'Unknown error'
          );
        }
      } catch (error) {
        result.failedCount += typeInteractions.length;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        result.errors.push(`${type}: ${errorMessage}`);
        this.localStorageManager.markSyncFailed(
          typeInteractions.map(i => i.id), 
          errorMessage
        );
      }
    }

    result.success = result.failedCount === 0;
    return result;
  }

  /**
   * 按类型分组交互
   */
  private groupInteractionsByType(interactions: UserInteraction[]): Record<string, UserInteraction[]> {
    const groups: Record<string, UserInteraction[]> = {};

    interactions.forEach(interaction => {
      const groupKey = `${interaction.targetType}_${interaction.type}`;
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(interaction);
    });

    return groups;
  }

  /**
   * 同步特定类型的交互组
   */
  private async syncInteractionGroup(type: string, interactions: UserInteraction[]): Promise<{success: boolean, error?: string}> {
    const endpoint = this.getEndpointForType(type);
    if (!endpoint) {
      return { success: false, error: `No endpoint for type: ${type}` };
    }

    const requestData = {
      interactions: interactions.map(this.transformInteractionForAPI),
    };

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `HTTP ${response.status}: ${errorText}` };
    }

    const data = await response.json();
    return { success: data.success, error: data.error };
  }

  /**
   * 获取对应类型的API端点
   */
  private getEndpointForType(type: string): string | null {
    const endpoints: Record<string, string> = {
      'post_like': '/api/interactions/batch-likes',
      'post_unlike': '/api/interactions/batch-likes',
      'post_bookmark': '/api/interactions/batch-bookmarks',
      'post_unbookmark': '/api/interactions/batch-bookmarks',
      'comment_comment_like': '/api/interactions/batch-comment-likes',
      'comment_comment_unlike': '/api/interactions/batch-comment-likes',
    };

    return endpoints[type] || null;
  }

  /**
   * 转换交互数据为API格式
   */
  private transformInteractionForAPI(interaction: UserInteraction) {
    return {
      id: interaction.id,
      type: interaction.type,
      targetId: interaction.targetId,
      targetType: interaction.targetType,
      userId: interaction.userId,
      ipAddress: interaction.ipAddress,
      timestamp: interaction.timestamp,
    };
  }

  /**
   * 获取同步状态
   */
  public getSyncStatus() {
    return {
      isSyncing: this.isSyncing,
      queueLength: this.syncQueue.length,
      ...this.localStorageManager.getSyncStatus(),
    };
  }

  /**
   * 手动触发同步
   */
  public async manualSync(): Promise<SyncResult> {
    return this.syncPendingInteractions(true);
  }

  /**
   * 清理已同步的数据
   */
  public cleanupSyncedData(): void {
    this.localStorageManager.cleanupSyncedData();
  }

  /**
   * 重置同步状态（用于测试）
   */
  public resetSyncState(): void {
    this.isSyncing = false;
    this.syncQueue = [];
  }
}

export default BatchSyncService;
